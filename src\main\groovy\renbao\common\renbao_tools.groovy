package renbao.common




def fixDistrict(code) {
    //默认+2 以下区域已停用，要改为映射值，银川市区640101 厦门市区350201
    [
            '140102' : '140105', // 太原小店区
            '152902' : '152921', // 阿拉善左旗
            '230802' : '230805', // 佳木斯市东风区
            '232702' : '232721',// 大兴安岭呼伦县
            '310102' : '310101',// 上海黄浦区
            '320202' : '320205',// 无锡市锡山区
            '320502' : '320505',// 苏州市虎丘区
            '320501' : '320505',// 苏州市虎丘区
            '320702' : '320703',// 连云港市连云区
            '330202' : '330205',// 宁波市江北区
            '340502' : '340503',// 马鞍山市花山区
            '340702' : '340705',// 铜陵市铜官区
            '350202' : '350205',// 厦门市海沧区
            '370802' : '370811',// 济宁市任城区
            '419002' : '419001',//河南省直辖县
            '421302' : '421303',// 随州市曾都区
            '429002' : '429004',// 湖北仙桃市
            '430402' : '430405',// 衡阳市珠晖区
            '433102' : '433101',//湘西土家族苗族自治州市辖区
            '440102' : '440105',// 广州市海珠区
            '440202' : '440205',// 韶关市曲江区
            '440302' : '440305',// 深圳市南山区
            '440502' : '440507',// 汕头市龙湖区
            '440602' : '440605',// 佛山市南海区
            '440702' : '440705',// 江门市新会区
            '450402' : '450405',// 梧州市长洲区
            '460102' : '460105',// 海口市秀英区
            '460202' : '460205',// 三亚市崖州区
            '510102' : '510105',// 成都市青羊区
            '510602' : '510603',// 德阳市旌阳区
            '510702' : '510703',// 绵阳市涪城区
            '510902' : '510903',// 遂宁市船山区
            '513202' : '513221',// 阿坝羌族自治州辖区
            '513402' : '513401',// 凉山彝族自治州辖区
            '520202' : '520201',// 六盘水市辖区
            '522302' : '522301',// 黔西南布依族苗族自治州辖区
            '522602' : '522601',// 黔东南苗族侗族自治州辖区
            '532302' : '532301',// 楚雄彝族自治州辖区
            '532602' : '532622',// 文山壮族苗族自治州辖区
            '532802' : '532801',// 西双版纳傣族自治州辖区
            '532902' : '532901',// 大理白族自治州辖区
            '533302' : '533321',// 怒江傈僳族自治州辖区
            '533402' : '533422',// 迪庆藏族自治州辖区
            '542402' : '542421',// 那曲地区
            '542502' : '542521',// 阿里地区
            '620202' : '620201',// 嘉峪关市辖区
            '622902' : '622901',// 临夏回族自治州辖区
            '623002' : '623001',// 甘南藏族自治州辖区
            '632202' : '632221',// 海北藏族自治州辖区
            '632302' : '632321',// 黄南藏族自治州辖区
            '632502' : '632521',// 海南藏族自治州辖区
            '632602' : '632621',// 果洛藏族自治州辖区
            '632702' : '632722',// 玉树藏族自治州辖区
            '640102' : '640105',// 银川市西夏区
            '652702' : '652701',// 博尔塔拉蒙古自治州辖区
            '652802' : '652801',// 巴音郭楞蒙古自治州辖区
            '652902' : '652901',// 阿克苏地区
            '653002' : '653001',// 克孜勒克自治州辖区
            '653102' : '653101',// 喀什地区
            '653202' : '653201',// 和田地区
            '654302' : '654301',// 阿勒泰地区
            '410307' : '410381',// 洛阳偃师
            '410308' : '410322',// 洛阳孟津
            '460401': '460400',// 儋州市
            '420101': '420103',// 武汉市辖区转江汉区
            '130401': '130402', // 邯郸市辖区转邯山区
            '451401': '451402', // 崇左市市辖区转江州区
            '410101': '410102', // 郑州市市辖区转郑州市中原区
            '510101': '510107', // 成都市辖区转成 武侯区
            '421001': '421002', // 荆州沙市区
            '420901': '420902', // 孝感市孝南区
            '440301': '440303', // 深圳罗湖
            '511028': '511002', // 内江市中区
            '510132': '510131', // 成都蒲江县
            '420701': '420704', // 鄂州市鄂城区
            '420801': '420802', // 荆门市东宝区
            '511101': '511102', // 乐山市中区
            '421101': '421102', // 黄冈黄州区
            '510601': '510603', // 德阳市旌阳区
            '510180': '510185', //  简阳市
            '510701': '510703', // 绵阳涪城区
            '441902': '441900', // 东莞市
            '441901': '441900', // 东莞市
            '320602': '320612', // 南通市通州区
            '511821': '511822', // 雅安 荥经县
            '330601': '330602', // 绍兴越城区
            '420601': '420602', // 襄阳市襄城区
            '320871': '320803', // 淮安市淮安区
            '510901': '510903', // 遂宁市船山区
            '610101' : '610102', // 西安市新城区
            '220101' : '220102', // 长春市南关区
            '500201' : '500154', // 重庆市开州区
            '320401' : '320402', // 常州市天宁区
            '130101' : '130102', // 石家庄长安区
            '442001' : '442000', // 中山
            '442002' : '442000', // 中山
            '440101' : '440103', // 广州荔湾
            '650501' : '650502', // 哈密市
            '533101' : '533102', // 云南瑞丽
            '520601' : '520602', // 铜仁市碧江区
            '411626' : '411625', // 周口郸城县
            '410901' : '410902', // 濮阳市华龙区
            '410728' : '410702', // 新乡红旗区
            '410171' : '410102', // 郑州中原区
            '320201' : '320205', // 无锡锡山区
            '130201' : '130202', // 唐山市路南区
            '650101' : '650102', // 乌鲁木齐市天山区
            '450101' : '450102', // 南宁兴宁区
            '411601' : '411602', // 周口市川汇区
            '411001' : '411002', // 许昌魏都区
            '410201' : '410202', // 开封龙亭区
            '210101' : '210102', // 沈阳和平区
            '320801' : '320803', // 淮安市淮安区
            '510501' : '510502', // 泸州市江阳区
            '371301' : '371302' // 临沂市兰山区

    ].get(code) ?: code
}

def getClauseCodeByAreaCode(areaCode) {
    [
            '110000' : ['01', '02', '04', '05', '06', '07', '09', '10'], // 北京
            '610000' : ['01', '05', '06', '08', '09'], // 陕西
            '420000' : ['01', '02', '04', '08', '10'], // 湖北
            '410000' : ['01', '02', '04', '05', '06', '07', '08', '09', '10'], // 河南
            '330000' : ['10'], // 浙江
            '520000' : ['01', '02', '04', '05', '06', '07', '08', '09', '10'], // 贵州
            '210000' : ['01', '02', '03', '04', '05', '06', '07', '09', '10'], // 辽宁
            '650000' : ['01', '02', '04', '05', '06', '07', '08', '09', '10'], // 新疆
            '410300' : ['01', '02', '04', '05', '06', '07', '10'], // 洛阳
            '350200' : ['01', '04', '10'], // 厦门
            '450000' : ['01', '07', '10'], // 广西
            '120000' : ['01', '02', '04', '05', '06', '07', '08', '10'], // 天津
            '410700' : ['10'], // 新乡
            '420600' : ['10'], // 湖北襄阳
            '370300' : ['05', '06', '10'], // 山东淄博
            '460000' : ['10'], // 海南
            '330600' : ['01', '02', '03', '04', '05', '06', '07', '08', '10'], // 浙江绍兴
            '640000' : ['02', '04', '05', '06', '08', '10'], // 宁夏
            '130900' : ['01', '02', '04', '05', '06', '07', '08', '10'], // 沧州
            '130600' : ['01', '02', '04', '05', '06', '07', '08', '09', '10'], // 保定
            '130300' : ['01', '02', '04', '05', '06', '07', '08', '09', '10'], // 秦皇岛
            '371100' : ['01', '04', '05', '06', '09'], // 日照
            '540000' : ['01', '02', '04', '05', '07'], // 西藏
            '140100' : ['01', '02', '03', '04', '05', '06', '07', '08', '10'], // 太原
            '130700' : ['01', '02', '04', '05', '06', '07', '08', '10'], // 张家口
            '130400' : ['01', '02', '04', '05', '06', '07', '08', '10'], // 邯郸
            '130500' : ['01', '02', '04', '05', '06', '07', '08', '10'], // 邢台
            '210300' : ['01', '02', '03', '04', '05', '06', '07', '10'], // 鞍山
            '130200' : ['01', '02', '04', '05', '06', '07', '08', '09', '10'], // 唐山
            '131000' : ['01', '02', '04', '05', '06', '07', '08', '09', '10'], // 廊坊
            '131100' : ['01', '02', '04', '05', '06', '07', '08', '10'], // 衡水
            '411500' : ['01', '02', '03', '04', '05', '06', '07', '10'], // 信阳
            '411000' : ['01', '02', '03', '04', '07', '09', '10'], // 许昌
            '371300' : ['01', '02', '03', '04', '05', '06', '07', '08', '10'], // 临沂
            '320200' : ['10'], // 无锡
            '140400' : ['01', '02', '03', '04', '05', '06', '07', '08', '10'], // 长治
            '410500' : ['10'] // 河南安阳
    ].get(areaCode)
}

def getClauseNameByCode(code) {
    [
            '01': '发动机检测（机油、空滤、燃油、冷却等）',
            '02': '变速器检测',
            '03': '转向系统检测（含车轮定位测试、轮胎动平衡测试）',
            '04': '底盘检测',
            '05': '轮胎检测',
            '06': '汽车玻璃检测',
            '07': '汽车电子系统检测（全车电控电器系统检测）',
            '08': '车内环境检测',
            '09': '蓄电池检测',
            '10': '车辆综合安全检测'
    ].get(code)
}

def sichuanLicensePrefix(cityCode) {
    [
            '510100': '暂A', // 成都
            '510300': '暂C', // 自贡
            '510400': '暂D', // 攀枝花
            '510500': '暂E', // 泸州
            '510600': '暂F', // 德阳市
            '510700': '暂B', // 绵阳
            '510800': '暂H', // 广元
            '510900': '暂J', // 遂宁市
            '511000': '暂K', // 内江
            '511100': '暂L', // 乐山市
            '511300': '暂R', // 南充
            '511400': '暂Z', // 眉山市
            '511500': '暂Q', // 宜宾市
            '511600': '暂X', // 广安市
            '511700': '暂S', // 达州市
            '511800': '暂T', // 雅安市
            '511900': '暂Y', // 巴中市
            '512000': '暂M', // 资阳市
            '513200': '暂U', // 阿坝藏族羌族自治州
            '513300': '暂V', // 甘孜藏族自治州
            '513400': '暂W' // 凉山彝族自治州

    ].get(cityCode)
}

