package common.pdfAnalysis.company.pingan

import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.LocalDateTimeUtil
import common.pdfAnalysis.base.pdf_analysis_base
import common.pdfAnalysis.company.pingan.regular.pdf_2007_regular

import common.pdfAnalysis.common.pdf_analysis_utils

/**
 *
 * Created by liuyong on 2024/1/19
 */
class pdf_2007_analysis extends pdf_analysis_base {

    @Override
    def analysisPdf(param) {
        super.analysisPdf(param)
    }

    @Override
    def regular(param) {
        //设置规则数据
        def regular = new pdf_2007_regular()
        def cityCode = param?.cityCode
        switch (cityCode) {
            default:
                break

        }
        regular
    }


    /**
     * 获取险种信息
     * @param text
     */
    @Override
    def suitTextArray(text) {

        def arr = [['保费合计(元)', 'RMB'], ['保费合计(元)', '保险费合计'], ['服务次数', '保险费合计'], ['保险费（元）', '特别提示：']]
        def beginIndex = -1
        def endIndex = -1
        for (item in arr) {
            beginIndex = text.indexOf(item[0])
            endIndex = text.indexOf(item[1])
            if (beginIndex != -1 && endIndex != -1) {
                beginIndex += item[0].length()
                break
            }
        }
        if (beginIndex == -1 || endIndex == -1) {
            return
        }
//        def textArray = text?.substring(beginIndex, endIndex)?.trim()?.replaceAll('）', '')?.split('\n')
        def textArray = text?.substring(beginIndex, endIndex)?.trim()?.split('\n')

        def suitTextArray = []
        def tempStr = ''
        def tempArray = []

        /*
         * 附加医保外医疗费用责任险（车上人员乘客
            共享保额 5.06 — 5.06
            ）
         */
        textArray.each {
            boolean isEnd = false
            if (tempStr) {
                if (tempStr.contains('（')) {
                    if (it.contains('）')) {
                        tempArray.add(it)
                        def temp = ''
                        if (tempArray.size() >=3) {
                            temp = (tempArray[0]?.trim() + tempArray[2]?.trim())?.replaceAll(' ', '') + ' ' + tempArray[1]
                        }
                        if (temp) {
                            suitTextArray.add(temp.trim())
                        }
                        isEnd = true
                    } else {
                        tempStr += it.trim() + ' '
                        tempArray.add(it)
                    }
                } else {
                    if (pdf_analysis_utils.endWithNumber(it) || it.endsWith('—')) {
                        suitTextArray.add(pdf_analysis_utils.filterSpace(tempStr.trim()?.replaceAll(' ','') + ' ' + it, '款'))
                        isEnd = true
                    } else {
                        tempStr += it.trim() + ' '
                        tempArray.add(it)
                    }
                }
            } else if (pdf_analysis_utils.endWithNumber(it) || it.endsWith('—')) {
                suitTextArray.add(pdf_analysis_utils.filterSpace(it, '款'))
                isEnd = true
            } else {
                tempStr = it.trim() + ' '
                tempArray.add(it)
            }

            if (isEnd) {
                tempStr = ''
                tempArray = []
            }
        }
        suitTextArray
    }


    /**
     * 商业险险种明细获取
     * @param text
     * @return
     */
    @Override
    def parseSuites(textArray) {

        def suites = []

        if (!textArray) {
            return suites
        }

        def removeSeatRegex = ~/[0-9]+座/
        def cleanNumberRegex = /[^\d.]/

        (textArray as List)?.each { it ->
            if (it) {

                def suite = [:]

                def itemStr = it.replaceAll(removeSeatRegex, '')?.replaceAll('次', '')

                def itInfo = itemStr.split(' ').findAll { it != '' }

                if (itInfo.size() < 4) {
                    return
                }

                itInfo -= '—'
                suite.code = itInfo[0]
                def amount = itInfo[1]
                suite.amount =  amount?.replaceAll(cleanNumberRegex, '')
                if (amount?.contains('万') && amount?.contains('座') && suite.amount) {
                    suite.amount = (suite.amount as BigDecimal)*10000 + ''
                }
                if (amount?.contains('共享保额')) {
                    suite.share = true
                }

                suite.discountCharge = itInfo?.size()>2 ? itInfo[2] : '0'

                if (suite.discountCharge) {
                    suite.discountCharge = suite.discountCharge.replaceAll(cleanNumberRegex, '') ?: '0'
                }

                suites << suite
            }
        }
        suites
    }

    /**
     * 特殊处理子类实现
     */
    @Override
    def specialAnalysis() {
        //登记日期 2024年3月
        if (resultData.carInfo.firstRegDate && resultData.carInfo.firstRegDate.contains('年')) {
            if (resultData.carInfo.firstRegDate.contains('日')) {
                resultData.carInfo.firstRegDate = pdf_analysis_utils.transDateTime(resultData.carInfo.firstRegDate + ' 00:00')
            } else {
                resultData.carInfo.firstRegDate = pdf_analysis_utils.transDateTime(resultData.carInfo.firstRegDate + '1日 00:00')
            }
        } else {
            resultData.carInfo.firstRegDate = resultData.carInfo.firstRegDate + ' 00:00:00'
        }
    }

    //矫正投保时间
    // "2023年12月19日15:01"
    @Override
    def fitBizOrderDate(orderDate) {
        formatDate(orderDate)
    }


    @Override
    def fitEfcOrderDate(orderDate) {
        formatDate(orderDate)
    }

    //矫正投保时间
    // "2023年12月19日15:01"
    // 2024-09-22 12:48:40 甘肃地区
    @Override
    def formatDate(Object timeStr) {

        if (!timeStr) return timeStr
        boolean isCorrectDate = true
        try {
            LocalDateTimeUtil.parse(timeStr, DatePattern.NORM_DATETIME_PATTERN)
        } catch (Exception ignored) {
            isCorrectDate = false
        }
        if (isCorrectDate) {
            return timeStr
        } else {
            timeStr = (timeStr as String).replaceAll(':', '时')
            pdf_analysis_utils.formatDate(timeStr)
        }
    }

    @Override
    def cleanData() {
        super.cleanData()
        //证件类型
        if (resultData?.insuredPersonInfoList[0]) {
            def idCardType = resultData?.insuredPersonInfoList[0]?.idCardType ? resultData?.insuredPersonInfoList[0]?.idCardType : ''
            if (!idCardType) {
                if (resultData?.insuredPersonInfoList[0]?.name
                    && (resultData?.insuredPersonInfoList[0]?.name as String).length() <= 5) {
                    resultData?.insuredPersonInfoList[0]?.idCardType = 0
                } else {//非个人 增加使用性质判断
                    resultData?.insuredPersonInfoList[0]?.idCardType = 8
                    resultData?.carInfo?.useProps = 10
                }
            }
        }

    }


}
