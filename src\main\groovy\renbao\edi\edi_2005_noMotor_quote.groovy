package renbao.edi

import com.cheche365.bc.exception.TempSkipException
import renbao.common.renbao_tools

import static common.common_all.*
import static renbao.common.renbao_dict.*

import java.time.LocalDate
import java.time.format.DateTimeFormatter

import static renbao.common.renbao_util.getIDCardValidDate



if ((!enquiry?.SQ?.containsKey("nonMotor")) || enquiry?.SQ['nonMotor']['productCode'].contains('LCO')) {
    throw new TempSkipException(1, "无非车，跳过当前步骤！")
}

def script = new edi_common_2005()
def motorStart = enquiry?.baseSuiteInfo?.bizSuiteInfo?.start ?: enquiry?.baseSuiteInfo?.efcSuiteInfo?.start
def sDate = motorStart?.substring(0, 10)
def start = motorStart?.contains("00:00:00") ? sDate : script.caculateDay(sDate, 1, 0)
def end = script.caculateDay(start, -1, 1)
def configTel = getMobile()
def appEmail = config?.policyEmail ?: (getSupplyParam(enquiry, 'applicantEmail') ?: configTel + "@qq.com")
def appName = enquiry?.insurePerson?.name
def appIdentifyNumber = enquiry?.insurePerson?.idCard
def appIdentifyType = identifyType(enquiry?.insurePerson?.idCardType)
def appInsuredNature = [6, 8, 9, 10].contains(enquiry?.insurePerson?.idCardType) ? 2 : 1
def appSex = appInsuredNature == 1 ? (enquiry?.insurePerson?.sex ? (enquiry?.insurePerson?.sex + 1) + '' : '1') : ''
def appUnifiedSocialCreditCode = appInsuredNature == 2 ? enquiry?.insurePerson?.idCard : ""
def appMobile = appInsuredNature == 1 ? (taskType?.contains("quote") ? configTel : (enquiry?.insurePerson?.mobile ?: getPersonSupply(autoTask, 'applicantMobile'))) : configTel
def insuredPerson = enquiry?.insuredPersonList?.get(0)//被保人信息
def insuredName =  insuredPerson?.name
def insuredInsuredNature = [6, 8, 9, 10].contains(insuredPerson?.idCardType) ? 2 : 1 //1个人 2 团体
def insuredSex = insuredInsuredNature == 1 ? (insuredPerson?.sex ? (insuredPerson?.sex + 1) + '' : '1') : ''
def insuredMobile = insuredInsuredNature == 1 ? (taskType?.contains("quote") ? configTel : (insuredPerson?.mobile ?: getPersonSupply(autoTask, 'insuredMobile'))) : configTel
def insuredIdentifyNumber = insuredPerson?.idCard
def insuredUnifiedSocialCreditCode = insuredInsuredNature == 2 ? insuredPerson?.idCard : ""
def insuredIdentifyType = identifyType(insuredPerson?.idCardType)
def noMotorCode = enquiry.SQ.nonMotor?.productCode
def count = enquiry.SQ.nonMotor?.count ?: 1
def operate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
def licenseNo =  enquiry?.carInfo?.plateNum
def useNatureCode = useNatureCode(enquiry?.carInfo?.useProps)
def frameNo = enquiry?.carInfo?.vin
def engineNo = enquiry?.carInfo?.engineNum
def seatCount = enquiry?.carInfo?.seatCnt
def zoneNo = script.getZoneNo(enquiry, config)
def nonMotorQuoteUrl = "https://esb.epicc.com.cn/InterfaceProxy/prpins/ApplyJSONServlet?zoneNo=" + zoneNo
tempValues = tempValues ?: [:]
tempValues.nonMotorQuoteUrl = nonMotorQuoteUrl
def serviceManager = config?.serviceManagerCode ?: config?.CarChecker
def modeMap = [
        'ZBM' : 'INTZBM32048200000001',
        'EAD' : 'INTEAD32048200000001',
        'EAU' : 'INTEAU32048200000001',
        'YEL' : 'INTYEL32048200000001',
        'EBS' : 'INTEBS32048200000001',
        'JDI' : 'INTJDI32048200000001'
]
def modeId = modeMap[(noMotorCode?.substring(0, 3) as String)] ?: ''
def platformProjectCode = config?.platformprojectcode ?: ''
def ext1 = config?.ext1 ?: 'CXF'
def appAddress = getPersonSupply(autoTask, 'applicantAddress')
def appProvince = appAddress && appAddress['province'] ? appAddress['province'] : (enquiry?.deliverInfo?.province ? (enquiry?.deliverInfo?.province + '') :  enquiry?.insArea['province'])
def appCity = appAddress && appAddress['city'] ? appAddress['city'] : (enquiry?.deliverInfo?.city ? (enquiry?.deliverInfo?.city + '') : enquiry?.insArea['city'])
def appArea = appAddress && appAddress['district'] ? appAddress['district'] : (enquiry?.deliverInfo?.area ? (enquiry?.deliverInfo?.area + '') : (((enquiry?.insArea['city'] as int) + 2) as String))
def tools = new renbao_tools()
appArea = tools.fixDistrict(appArea)
def appAddressDetail = appAddress && appAddress['detail'] ? appAddress['detail'] : config?.address
def businessNature = (config?.nonMotorBizOrigin ?: config?.BizOrigin) ?: "2"
def visaCodeE = config?.visaCodeE ?: (noMotorCode.startsWith("EBS") ? 'EEEBSC00181' : 'EELCOA00210')

def applicantIdCardValidDate = getIDCardValidDate(autoTask, 'applicant')
def appDateValid = applicantIdCardValidDate['end']
def appDateValidStart = applicantIdCardValidDate['start']
def appBirthday = [6, 8, 9, 10].contains(enquiry?.insurePerson?.idCardType) ? '' : enquiry?.insurePerson?.birthday
def appUnitType = [6, 8, 9, 10].contains(enquiry?.insurePerson?.idCardType) ? '300' : ''
def appCountryCode = [5, 12, 14].contains(enquiry?.insurePerson?.idCardType) ? getCountryCode(enquiry?.insurePerson?.idCardType, enquiry?.insurePerson?.idCard) : 'CHN'

def insuredIdCardValidDate = getIDCardValidDate(autoTask, 'insured')
def insuredDateValid = insuredIdCardValidDate['end']
def insuredDateValidStart = insuredIdCardValidDate['start']

def insuredAddress = getPersonSupply(autoTask, 'insuredAddress')
def insuredProvince = insuredAddress && insuredAddress['province'] ? insuredAddress['province'] : (enquiry?.deliverInfo?.province ? (enquiry?.deliverInfo?.province + '') :  enquiry?.insArea['province'])
def insuredCity = insuredAddress && insuredAddress['city'] ? insuredAddress['city'] : (enquiry?.deliverInfo?.city ? (enquiry?.deliverInfo?.city + '') : enquiry?.insArea['city'])
def insuredArea = insuredAddress && insuredAddress['district'] ? insuredAddress['district'] : (enquiry?.deliverInfo?.area ? (enquiry?.deliverInfo?.area + '') : (((enquiry?.insArea['city'] as int) + 2) as String))
insuredArea = tools.fixDistrict(insuredArea)
def insuredAddressDetail = insuredAddress && insuredAddress['detail'] ? insuredAddress['detail'] : config?.address
def insuredUnitType = [6, 8, 9, 10].contains(insuredPerson?.idCardType) ? '300' : ''
def insuredCountryCode = [5, 12, 14].contains(insuredPerson?.idCardType) ? getCountryCode(insuredPerson?.idCardType, insuredPerson?.idCard) : 'CHN'
def agencyCode = config?.nonMotorChannelCode ?: config.ChannelCode
def contactName = getSupplyParam(enquiry, 'liaisonName') ?: "张先生"
def insuredBirthday = [6, 8, 9, 10].contains(insuredPerson?.idCardType) ? '' : insuredPerson?.birthday

def jsonStr = """[{
          "clientIp":"1",
          "contractNo":"",
          "agencyCode":"${agencyCode ?:''}",
          "businessNature": "${businessNature}",
          "comCode":"${config?.nonMotorComCode ?: config?.ComCode}",
          "startDate":"${start}",
          "startHour":"0",
          "endDate":"${end}",
          "endHour":"24",
          "makeCode":"${config.nonMotorMakeCom ?: config?.MakeCom}",
          "modeType":"0",
          "modeid":"${modeId}",
          "serviceManager":"${serviceManager}",
          "operateDate":"${operate}",
          "operatorCode":"${config.nonMotorUserCode ?: config?.UserCode}",
          "pexti1":"1",
          "isAccredit" : "1",
          "pexti2":"${seatCount}",
          "platformProjectCode" : "${platformProjectCode}",
          "ext1" : "${ext1}",
          "guardian_03s":[
            {
                "gexts3":"00:00",
                "gexts4":"00:00",
                "serialNo":"1"
            }
          ],
          "guardian_04s":[
            {
                "gexti1":"${seatCount}",
                "gexts1":"${licenseNo}",
                "gexts2":"${useNatureCode}",
                "gexts3":"${frameNo}",
                "gexts4":"${engineNo}",
                "serialNo":"1"
            }
           ],
          "handler1Code":"${config.nonMotorHandler1Code ?: config?.Handler1Code}",
          "handlerCode":"${config.nonMotorHandlerCode ?: config?.HandlerCode}",
          "insuredInfos":
          [
            
             {
                "email":"${appEmail}",
                "sex":"${appSex}",
                "countryCode" : "${appCountryCode}",
                "insuredFlag" : "10000000000000000000000000000A",
                "identifyNumber":"${appIdentifyNumber}",
                "identifyType":"${appIdentifyType}",
                "insuredName":"${appName}",
                "insuredType":"${appInsuredNature}",
                "birthday" : "${appBirthday}",
                "insuredIdentity":"0",
                "occupationCode" : "000103",
                "province": "${appProvince}",
                "city": "${appCity}",
                "area": "${appArea}",
                "postAddress" : "${appAddressDetail}",
                "insuredAddress" : "${appAddressDetail}",
                "startDateValid": "${appDateValidStart}",
                "dateValid": "${appDateValid}",
                "mobile":"${appMobile}","""
                if (insuredInsuredNature == 2) {
                    jsonStr += """
                        "unittype" : "${appUnitType}",
                        "unifiedSocialCreditCode" : "${appUnifiedSocialCreditCode}",
                        "linkerName" : "${contactName}",
                    """
                }
jsonStr += """
                "serialNo":"1"
             },
               {
                "email":"${appEmail}",
                "sex":"${insuredSex}",
                "countryCode" : "${insuredCountryCode}",
                "insuredFlag" : "010000000000000000000000000000",
                "identifyNumber":"${insuredIdentifyNumber}",
                "identifyType":"${insuredIdentifyType}",
                "insuredName":"${insuredName}",
                "insuredType":"${insuredInsuredNature}",
                "birthday" : "${insuredBirthday}",
                "insuredIdentity":"0",
                "occupationCode" : "000103",
                "province": "${insuredProvince}",
                "city": "${insuredCity}",
                "area": "${insuredArea}",
                "postAddress" : "${insuredAddressDetail}",
                "insuredAddress" : "${insuredAddressDetail}",
                "startDateValid": "${insuredDateValidStart}",
                "dateValid": "${insuredDateValid}",
                "mobile":"${insuredMobile}","""
               if (insuredInsuredNature == 2) {
                    jsonStr += """
                     "unittype" : "${insuredUnitType}",
                     "unifiedSocialCreditCode" : "${insuredUnifiedSocialCreditCode}",
                     "linkerName" : "${contactName}",
                    """
                }
jsonStr += """ 
                "serialNo":"2"
             }
          ],
          "applyVisaPrintBodys":[
            {
              "visaCode": "${visaCodeE}",
              "messageWhether": "1",
              "emailWhether": "1"
            }
          ],
          "itemKindPlans":
             [
                {
                   "planCode":"${noMotorCode}",
                   "serialNo":"1",
                   "unit":"${count}",
                   "quantity" : "${count}"
                }
             ]
} ]"""
jsonStr
