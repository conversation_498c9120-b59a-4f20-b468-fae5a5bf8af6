{"changeSuites": {"add": [{"amount": 1, "code": "DesignatedDriving", "name": "附加机动车增值服务特约条款（代为驾驶服务）"}, {"amount": 2, "code": "RoadsideService", "name": "附加机动车增值服务特约条款（道路救援服务）"}], "update": [], "remove": []}, "insComId": "2011", "agentInfo": {"idCardType": 0, "idCard": "320123198906143819", "mobile": "***********", "name": "王亮", "misc": {}}, "carOwnerInfo": {"birthday": "1989-06-14", "censusRegister": "320123", "idCard": "320123198906143819", "sex": 0, "mobile": "***********", "idCardType": 0, "name": "王亮", "email": "<EMAIL>"}, "insuredPersonInfoList": [{"birthday": "1989-06-14", "censusRegister": "320123", "idCard": "320123198906143819", "sex": 0, "mobile": "***********", "idCardType": 0, "name": "王亮", "email": "<EMAIL>"}], "businessId": "2025060300115006@2011", "insArea": {"province": "320000", "city": "320100"}, "operatorName": "吴龙杰", "supplyParam": [{"itemvalue": "***********", "itemcode": "insuredMobile"}, {"itemvalue": "1", "itemcode": "neChargerSequenceNo"}, {"itemvalue": "2044-06-03", "itemcode": "insuredIDCardValidDate"}, {"itemvalue": "北京", "itemcode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"itemvalue": "2", "itemcode": "neChargerLocationType"}, {"itemvalue": "江苏省|320000#南京市|320100#六合区|320116#南京市六合区金牛湖街道和仁村竹山庄33号", "itemcode": "applicantAddress"}, {"itemvalue": "2024-06-03", "itemcode": "insuredIDCardRegistrationDate"}, {"itemvalue": "KUNMING", "itemcode": "neChargerNo"}, {"itemvalue": "3", "itemcode": "ne<PERSON><PERSON><PERSON>"}, {"itemvalue": "<EMAIL>", "itemcode": "applicantEmail"}, {"itemvalue": "2044-06-03", "itemcode": "applicantIDCardValidDate"}, {"itemvalue": "1", "itemcode": "neChargerType"}, {"itemvalue": "***********", "itemcode": "applicantMobile"}, {"itemvalue": "***********", "itemcode": "ownerMobile"}, {"itemvalue": "2024-06-03", "itemcode": "applicantIDCardRegistrationDate"}, {"itemvalue": "120KW一体式直流充电桩", "itemcode": "neCharger<PERSON>l"}, {"itemvalue": "南京市六合区金牛湖街道和仁村竹山庄33号", "itemcode": "owner<PERSON><PERSON><PERSON>"}], "baseSuiteInfo": {"bizSuiteInfo": {"discountRate": 1.27, "suites": [{"discountRate": 1.27, "amount": 283606, "code": "VehicleDamage", "name": "机动车损失保险", "discountCharge": 4938.27, "orgCharge": 3888.4}, {"discountRate": 1.27, "amount": 3000000, "code": "ThirdParty", "name": "机动车第三者责任保险", "discountCharge": 1990.34, "orgCharge": 1567.2}, {"discountRate": 1.27, "amount": 10000, "code": "Driver", "name": "机动车车上人员责任保险（司机）", "discountCharge": 30.92, "orgCharge": 24.35}, {"discountRate": 1.27, "amount": 10000, "code": "Passenger", "name": "机动车车上人员责任保险（乘客）", "discountCharge": 80.44, "orgCharge": 63.34}, {"discountRate": 1.27, "amount": 2, "code": "RoadsideService", "name": "道路救援服务特约条款", "discountCharge": 0, "orgCharge": 0}, {"discountRate": 1.27, "amount": 1, "code": "DesignatedDriving", "name": "代为驾驶服务特约条款", "discountCharge": 0, "orgCharge": 0}, {"discountRate": 1.27, "amount": 100000, "code": "NIHCThirdParty", "name": "附加医保外用药责任险（机动车第三者责任保险）", "discountCharge": 61.8, "orgCharge": 48.66, "share": false}, {"discountRate": 1.27, "amount": 1, "code": "NEGridBugDamage", "name": "附加外部电网故障损失险", "discountCharge": 63.56, "orgCharge": 50.05}], "start": "2025-07-03 00:00:00", "discountCharge": 7165.33, "end": "2026-07-03 00:00:00"}}, "appId": "F1E5CA11", "applicantPersonInfo": {"birthday": "1989-06-14", "censusRegister": "320123", "idCard": "320123198906143819", "sex": 0, "mobile": "***********", "idCardType": 0, "name": "王亮", "email": "<EMAIL>"}, "carInfo": {"yearPattern": "2023", "taxAnalogyPrice": "0", "carModelName": "理想LXA6502SHEVX1插电式增程混合动力多用途乘用车", "fullLoad": 2460, "modelLoad": 0, "noLicenseFlag": false, "plateColor": 2, "jyPrice": 346800, "listedYear": "202209", "price": 346800, "familyName": "理想L7", "vin": "LW433B129P1687870", "displacement": 1.496, "vehicleId": "4028d06d82d9265701857c357bb52e3f", "isLocalDriving": "1", "aliasName": "理想L7 Max", "analogyPrice": 0, "carPriceType": "0", "useProps": 1, "isTransfer": false, "plateType": 1, "firstRegDate": "2024-06-18 00:00:00", "engineNum": "0169290", "isLocalRegistration": "0", "isNew": false, "plateNum": "苏AFR8626", "carBrandName": "理想", "carUserType": 0, "syvehicletypename": "六座以下客车", "rbCode": "ZDGADD0002", "jgVehicleType": 13, "syvehicletypecode": "KA", "fuelType": 2, "seatCnt": 5, "jyCode": "ZDGADD0002", "taxPrice": 346800}, "configInfo": {"configMap": {"partnerClientType": 2, "terminalNo": "LX_CC_NANJINGDC", "isEApplicant": "1", "outerActivityCode": "LX_MCSBXBYGDBF_2022", "sellingChannel": "21", "domain": "https://nbc.chetimes.com", "arbitrationOrgName": "南京仲裁委员会", "payClientCode": "10", "apiType": 2, "isTPreminum": "0"}}, "imgAddress": {"3": "https://chetimes-obs.cdn.chetimes.com/upload/partner/file/jpg/2025060300115006%25402011/1748931446047Z0HBFEHKY0.jpg?cheche_token=1748931446-e1791222bd6347c99a0d26c596a7b6b0-0-8FE78FC325B594B9A105F1E80819D325E3F9B15AE0E8558E32C7895373DC834D", "4": "https://chetimes-obs.cdn.chetimes.com/upload/partner/file/jpg/2025060300115006%25402011/1748931446460DYKUBALWZM.jpg?cheche_token=1748931446-1bfd8622cd964fbcbc86df9ae05e6bc1-0-B65A95589877E75EF36C1D148B7B7710C9725297EF66F57033824CC74824311D", "25": "https://chetimes-obs.cdn.chetimes.com/upload/partner/file/jpg/2025060300115006%25402011/17489314454047CWU8QSVBH.jpg?cheche_token=1748931446-caa97929cf784bb38a119745032276e8-0-45E66DA274FDFF20DB74EC94544AAAF6571DF056C675BB6F343321E1AB005966", "26": "https://chetimes-obs.cdn.chetimes.com/upload/partner/file/jpg/2025060300115006%25402011/17489314458052L6EKJEDYO.jpg?cheche_token=1748931446-90dc6ee142d946938ee10e5567f701ef-0-BEAEC827F005B32A9CB07F1865A8061C2E2ED6344EDDE46D392AC4B7A6F7AD8B"}, "imgAssetId": {"3": "683e9376676cf800b4b48714", "4": "683e9376dceccb39804c5c34", "25": "683e9375676cf800b4b48712", "26": "683e93756fc5b721dbc1e7fd"}, "definition": {"selfRate": 1.27, "CpicTotalScore": "", "CpicEfcScore": "", "application.trafficScore": "", "CpicBizScore": "33", "application.totalScore": "", "CpicInsType": "2", "application.bizScore": "33", "application.loyalty ": "2"}, "processType": "edi", "sq": {"bizProposeNum": "ANAJ00LE2125G000728Q", "checkMsg": "", "bizCharge": 7165.33, "topOrderNo": "top44c1dd8f43f4d28d", "discountRates": {}, "isRuleTips": true, "message": {"BIZ_lastyear": "1.商业险重复投保！</br>\n2.重复投保的保险公司：中国大地财产保险股份有限公司</br>\n3.投保确认码：</br>\n4.保单号：PDEN24320113060000000243</br>\n5.车牌号：苏AFR8626</br>\n6.号牌种类：02</br>\n7.车架号：LW433B129P1687870</br>\n8.发动机号：0169290</br>\n9.起保日期：2024年07月02日 17时00分</br>\n10.终保日期：2025年07月03日 00时00分</br>\n11.签单日期：2024年07月02日 00时00分</br>\n12.已投保子险为：新能源汽车损失保险</br>新能源汽车第三者责任保险</br>附加医保外医疗费用责任险（第三者责任保险）</br>新能源汽车车上人员责任保险（司机）</br>附加医保外医疗费用责任险（车上人员责任保险（司机））</br>新能源汽车车上人员责任保险（乘客）</br>附加医保外医疗费用责任险（车上人员责任保险（乘客））</br>道路救援服务特约条款（新能源汽车）</br>车辆安全检测特约条款（新能源汽车）</br>代为驾驶服务特约条款（新能源汽车）</br>代为送检服务特约条款（新能源汽车）</br></br></br>"}, "totalCharge": 7753.33, "nonMotor": {"accidentProposeCode": "ANAJ00L30K25FM001I8R", "accidentPolicyCode": "ANAJ00L30K25FN0003IT", "productCode": "HL20240625A00068568.PDL20240625A00009669.PDL20240625A00009669PG004.PL2418980024A10000579", "count": 1, "discountCharge": 588}, "designatedDrivingMile": "10", "efcOrderNo": "efc44c1dd8f43f4d28d", "isRuleSuccess": "1", "bizOrderNo": "biz44c1dd8f43f4d28d", "misc": {"orderNo": "1002025061200117378720107", "businessId": "1002025061200117378720107", "branch": "3020100:江苏分公司"}}}